import type { Metada<PERSON> } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "<PERSON><PERSON> - Visual Artist",
  description: "Tehran-based visual artist and researcher exploring the intersection of design, art, and cultural expression.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className="antialiased font-inter">
        {children}
      </body>
    </html>
  );
}

